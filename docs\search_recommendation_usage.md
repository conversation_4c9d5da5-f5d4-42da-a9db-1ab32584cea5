# 搜索推荐功能使用指南

## 基本使用

### 1. API接口调用

```php
use app\api\logic\SearchRecordLogic;

// 获取搜索推荐关键词
public function getSearchRecommendations()
{
    // 获取当前用户ID（可能为null）
    $userId = $this->getUserId();
    
    // 调用推荐方法
    $result = SearchRecordLogic::findlists($userId);
    
    return $this->success('获取成功', $result);
}
```

### 2. 前端调用示例

```javascript
// 获取搜索推荐关键词
async function getSearchRecommendations() {
    try {
        const response = await fetch('/api/search/recommendations', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + getToken(), // 如果用户已登录
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.code === 1) {
            const keywords = data.data.find_lists;
            displaySearchKeywords(keywords);
        }
    } catch (error) {
        console.error('获取搜索推荐失败:', error);
    }
}

// 显示搜索关键词
function displaySearchKeywords(keywords) {
    const container = document.getElementById('search-keywords');
    container.innerHTML = '';
    
    keywords.forEach(keyword => {
        const keywordElement = document.createElement('span');
        keywordElement.className = 'search-keyword';
        keywordElement.textContent = keyword;
        keywordElement.onclick = () => searchByKeyword(keyword);
        container.appendChild(keywordElement);
    });
}

// 点击关键词进行搜索
function searchByKeyword(keyword) {
    document.getElementById('search-input').value = keyword;
    performSearch(keyword);
}
```

### 3. 返回数据格式

```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "find_lists": [
            "手机",
            "电脑",
            "服装",
            "家电",
            "美妆"
        ]
    }
}
```

## 高级用法

### 1. 自定义推荐策略

```php
// 为特定场景定制推荐逻辑
class CustomSearchRecommendation extends SearchRecordLogic
{
    /**
     * 首页搜索推荐（更注重热门度）
     */
    public static function getHomePageRecommendations($userId)
    {
        if (empty($userId)) {
            // 未登录用户直接返回全局热搜
            return self::getGlobalHotSearchKeywords();
        }
        
        // 已登录用户：70%个性化 + 30%热门
        $personalizedResult = self::getPersonalizedSearchKeywords($userId);
        $globalResult = self::getGlobalHotSearchKeywords();
        
        $personalizedKeywords = array_slice($personalizedResult['find_lists'], 0, 7);
        $globalKeywords = array_slice($globalResult['find_lists'], 0, 3);
        
        $mixedKeywords = array_merge($personalizedKeywords, $globalKeywords);
        $mixedKeywords = array_values(array_unique($mixedKeywords));
        
        return [
            'find_lists' => array_slice($mixedKeywords, 0, 10)
        ];
    }
    
    /**
     * 分类页面搜索推荐（更注重相关性）
     */
    public static function getCategoryPageRecommendations($userId, $categoryId)
    {
        $cacheKey = "category_search_recommendations_{$categoryId}_{$userId}";
        $cached = Cache::get($cacheKey);
        
        if ($cached) {
            return $cached;
        }
        
        $keywords = [];
        
        // 获取该分类下的热门商品关键词
        $categoryKeywords = self::getCategoryRelatedKeywords($categoryId);
        $keywords = array_merge($keywords, $categoryKeywords);
        
        // 如果用户已登录，添加个性化推荐
        if (!empty($userId)) {
            $personalizedResult = self::getPersonalizedSearchKeywords($userId);
            $personalizedKeywords = array_slice($personalizedResult['find_lists'], 0, 5);
            $keywords = array_merge($keywords, $personalizedKeywords);
        }
        
        $result = [
            'find_lists' => array_values(array_unique(array_slice($keywords, 0, 10)))
        ];
        
        Cache::set($cacheKey, $result, 1800); // 缓存30分钟
        return $result;
    }
}
```

### 2. 实时搜索建议

```php
/**
 * 实时搜索建议（输入时触发）
 */
public static function getSearchSuggestions($input, $userId = null, $limit = 8)
{
    if (empty($input) || mb_strlen($input, 'UTF-8') < 1) {
        return [];
    }
    
    $suggestions = [];
    
    // 1. 从用户历史搜索中匹配
    if (!empty($userId)) {
        $userHistory = SearchRecord::where([
            'user_id' => $userId,
            'del' => 0
        ])
        ->where('keyword', 'like', $input . '%')
        ->order('update_time DESC')
        ->limit(3)
        ->column('keyword');
        
        $suggestions = array_merge($suggestions, $userHistory);
    }
    
    // 2. 从全局热搜中匹配
    $globalSuggestions = SearchRecord::where('del', 0)
        ->where('keyword', 'like', $input . '%')
        ->field('keyword, COUNT(*) as search_count')
        ->group('keyword')
        ->order('search_count DESC')
        ->limit($limit - count($suggestions))
        ->column('keyword');
    
    $suggestions = array_merge($suggestions, $globalSuggestions);
    
    // 3. 从商品名称中匹配
    if (count($suggestions) < $limit) {
        $goodsSuggestions = Db::name('goods')
            ->where([
                'del' => 0,
                'status' => 1,
                'audit_status' => 1
            ])
            ->where('name', 'like', '%' . $input . '%')
            ->order('sales_actual + sales_virtual DESC')
            ->limit($limit - count($suggestions))
            ->column('name');
        
        // 从商品名称中提取关键词
        foreach ($goodsSuggestions as $goodsName) {
            $extractedKeywords = self::extractKeywordsFromGoodsName($goodsName);
            foreach ($extractedKeywords as $keyword) {
                if (stripos($keyword, $input) !== false) {
                    $suggestions[] = $keyword;
                }
            }
        }
    }
    
    // 去重并限制数量
    $suggestions = array_values(array_unique($suggestions));
    return array_slice($suggestions, 0, $limit);
}
```

### 3. 搜索热词趋势分析

```php
/**
 * 获取搜索热词趋势
 */
public static function getSearchTrends($days = 7)
{
    $trends = [];
    $startTime = time() - $days * 24 * 3600;
    
    // 按天统计搜索量
    for ($i = 0; $i < $days; $i++) {
        $dayStart = $startTime + $i * 24 * 3600;
        $dayEnd = $dayStart + 24 * 3600;
        
        $dayTrends = SearchRecord::where('del', 0)
            ->where('update_time', '>=', $dayStart)
            ->where('update_time', '<', $dayEnd)
            ->field('keyword, COUNT(*) as search_count')
            ->group('keyword')
            ->having('search_count >= 2')
            ->order('search_count DESC')
            ->limit(10)
            ->select()
            ->toArray();
        
        $trends[date('Y-m-d', $dayStart)] = $dayTrends;
    }
    
    return $trends;
}
```

## 性能优化建议

### 1. 缓存预热

```php
/**
 * 预热搜索推荐缓存
 */
public static function warmupSearchCache()
{
    // 预热全局热搜缓存
    self::getGlobalHotSearchKeywords();
    
    // 预热活跃用户的个性化推荐缓存
    $activeUsers = Db::name('search_record')
        ->where('update_time', '>=', time() - 24 * 3600)
        ->group('user_id')
        ->having('COUNT(*) >= 3')
        ->limit(100)
        ->column('user_id');
    
    foreach ($activeUsers as $userId) {
        try {
            self::getPersonalizedSearchKeywords($userId);
        } catch (\Exception $e) {
            // 忽略单个用户的错误
        }
    }
    
    echo "搜索推荐缓存预热完成\n";
}
```

### 2. 批量处理

```php
/**
 * 批量生成用户推荐
 */
public static function batchGenerateRecommendations($userIds)
{
    $results = [];
    
    foreach ($userIds as $userId) {
        try {
            $results[$userId] = self::findlists($userId);
        } catch (\Exception $e) {
            \think\facade\Log::error("用户 {$userId} 推荐生成失败: " . $e->getMessage());
            $results[$userId] = self::getGlobalHotSearchKeywords();
        }
    }
    
    return $results;
}
```

## 监控和调试

### 1. 推荐效果监控

```php
/**
 * 记录推荐关键词点击
 */
public static function recordKeywordClick($userId, $keyword, $source = 'recommendation')
{
    try {
        Db::name('search_keyword_click')->insert([
            'user_id' => $userId,
            'keyword' => $keyword,
            'source' => $source, // recommendation, search, suggestion
            'create_time' => time()
        ]);
    } catch (\Exception $e) {
        \think\facade\Log::error('记录关键词点击失败: ' . $e->getMessage());
    }
}

/**
 * 获取推荐效果统计
 */
public static function getRecommendationStats($days = 7)
{
    $startTime = time() - $days * 24 * 3600;
    
    // 推荐关键词点击率
    $recommendationClicks = Db::name('search_keyword_click')
        ->where('source', 'recommendation')
        ->where('create_time', '>=', $startTime)
        ->count();
    
    // 总搜索次数
    $totalSearches = SearchRecord::where('update_time', '>=', $startTime)
        ->count();
    
    $clickRate = $totalSearches > 0 ? ($recommendationClicks / $totalSearches) * 100 : 0;
    
    return [
        'recommendation_clicks' => $recommendationClicks,
        'total_searches' => $totalSearches,
        'click_rate' => round($clickRate, 2) . '%'
    ];
}
```

### 2. 调试工具

```php
/**
 * 调试用户推荐过程
 */
public static function debugUserRecommendation($userId)
{
    $debug = [
        'user_id' => $userId,
        'timestamp' => date('Y-m-d H:i:s'),
        'steps' => []
    ];
    
    try {
        // 1. 用户搜索历史
        $searchHistory = self::getUserSearchHistory($userId);
        $debug['steps']['search_history'] = [
            'count' => count($searchHistory),
            'keywords' => $searchHistory
        ];
        
        // 2. 分类推荐
        $categoryKeywords = self::getCategoryBasedKeywords($userId);
        $debug['steps']['category_keywords'] = [
            'count' => count($categoryKeywords),
            'keywords' => $categoryKeywords
        ];
        
        // 3. 购买历史推荐
        $purchaseKeywords = self::getPurchaseBasedKeywords($userId);
        $debug['steps']['purchase_keywords'] = [
            'count' => count($purchaseKeywords),
            'keywords' => $purchaseKeywords
        ];
        
        // 4. 相似用户推荐
        $similarUserKeywords = self::getSimilarUserKeywords($userId);
        $debug['steps']['similar_user_keywords'] = [
            'count' => count($similarUserKeywords),
            'keywords' => $similarUserKeywords
        ];
        
        // 5. 最终推荐结果
        $finalResult = self::findlists($userId);
        $debug['final_result'] = $finalResult;
        
    } catch (\Exception $e) {
        $debug['error'] = $e->getMessage();
    }
    
    return $debug;
}
```

## 常见问题解决

### 1. 新用户无推荐内容

```php
// 在 getPersonalizedSearchKeywords 方法中
if (count($topPersonalizedKeywords) < 5) {
    // 为新用户推荐热门分类
    $hotCategories = Db::name('goods_category')
        ->where('is_show', 1)
        ->where('level', 2)
        ->order('sort ASC')
        ->limit(5)
        ->column('name');
    
    $topPersonalizedKeywords = array_merge($topPersonalizedKeywords, $hotCategories);
}
```

### 2. 推荐结果过于单一

```php
// 增加多样性检查
$categoryCount = [];
$diverseKeywords = [];

foreach ($weightedKeywords as $keyword => $weight) {
    // 检查关键词所属分类
    $category = self::getKeywordCategory($keyword);
    $categoryCount[$category] = ($categoryCount[$category] ?? 0) + 1;
    
    // 限制同一分类的关键词数量
    if ($categoryCount[$category] <= 3) {
        $diverseKeywords[$keyword] = $weight;
    }
}
```

### 3. 性能问题优化

```php
// 设置执行时间限制
set_time_limit(3);

try {
    $result = self::getPersonalizedSearchKeywords($userId);
} catch (\Exception $e) {
    // 超时或错误时降级到全局热搜
    $result = self::getGlobalHotSearchKeywords();
}
```
