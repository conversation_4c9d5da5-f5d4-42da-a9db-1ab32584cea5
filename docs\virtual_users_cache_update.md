# 商品动态信息缓存清除功能说明

## 概述
本次修改将商品动态展示功能中的虚拟数据生成逻辑从随机生成改为从 `user_xu` 表中获取真实的头像和昵称数据，并添加了每天2点自动清除商品动态信息缓存的功能，确保虚拟数据的随机性。

## 修改内容

### 1. 数据库表结构
需要确保 `user_xu` 表存在并包含以下字段：
```sql
CREATE TABLE `ls_user_xu` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nickname` varchar(50) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2. 修改的文件

#### 2.1 `app/api/logic/GoodsDynamicLogic.php`
- **修改 `generateDummyData` 方法**：每次随机从 `user_xu` 表获取虚拟用户数据（不缓存）
- **新增 `clearGoodsDynamicCache` 方法**：清除所有商品的动态信息缓存

#### 2.2 `app/command/RefreshVirtualUsersCache.php`（新增）
- 定时任务命令类，用于清除商品动态信息缓存

#### 2.3 `config/console.php`
- 添加新的命令配置：`'refresh_virtual_users_cache' => 'app\command\RefreshVirtualUsersCache'`

#### 2.4 `database/add_virtual_users_cache_crontab.sql`（新增）
- 添加定时任务记录的SQL脚本

## 功能特性

### 1. 缓存机制
- 商品动态信息缓存（`goods_dynamic_info_{$goodsId}`）每天2点自动清除
- 清除后下次访问会重新生成，确保虚拟数据的随机性
- 支持手动清除缓存

### 2. 定时任务
- 每天凌晨2点自动清除所有商品的动态信息缓存
- 使用项目现有的定时任务系统
- 任务名称：`清除商品动态信息缓存`
- Cron表达式：`0 2 * * *`

### 3. 数据获取逻辑
- 每次随机从 `user_xu` 表获取30个用户的昵称和头像（不缓存用户数据）
- 只获取昵称不为空的用户记录
- 如果 `user_xu` 表为空，则使用默认昵称 `匿名用户`
- 如果头像为空，则使用原有的头像生成逻辑
- 确保每天的虚拟数据都是随机的

## 部署步骤

### 1. 执行SQL脚本
```bash
# 添加定时任务记录
mysql -u用户名 -p数据库名 < database/add_virtual_users_cache_crontab.sql
```

### 2. 手动清除缓存（可选）
```bash
# 手动执行一次缓存清除
php think refresh_virtual_users_cache
```

### 3. 启动定时任务系统
确保项目的定时任务系统正在运行：
```bash
php think crontab
```

## 测试验证

### 1. 验证命令是否正常
```bash
php think refresh_virtual_users_cache
```

### 2. 验证缓存清除是否生效
- 执行命令后检查商品动态信息缓存是否被清除
- 访问商品详情页，确认缓存重新生成

### 3. 验证商品动态展示
- 访问商品详情页
- 检查动态展示中的头像和昵称是否来自 `user_xu` 表
- 每天2点后检查虚拟数据是否有变化

## 注意事项

1. **数据准备**：确保 `user_xu` 表中有足够的测试数据
2. **权限检查**：确保定时任务有权限访问数据库和缓存
3. **错误监控**：定时任务执行失败会记录在 `dev_crontab` 表的 `error` 字段中
4. **性能考虑**：每次生成虚拟数据都会查询 `user_xu` 表，如果数据量很大可以考虑添加限制条件

## 回滚方案

如果需要回滚到原来的逻辑：
1. 恢复 `GoodsDynamicLogic.php` 中的 `generateDummyData` 方法
2. 删除定时任务记录：`DELETE FROM ls_dev_crontab WHERE command = 'refresh_virtual_users_cache'`
3. 商品动态信息缓存会自然过期，无需手动清除
