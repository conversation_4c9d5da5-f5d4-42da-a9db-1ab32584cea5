<?php

namespace app\api\controller;

use app\api\logic\ArticleLogic;
use app\common\basics\Api;
use app\common\server\JsonServer;
use think\facade\Validate;
use app\api\logic\ShopLogic;


class Shop extends Api
{
    public $like_not_need_login = ['qualifications','getexecuteCollectiveProcurement','getHotManufacturers','getverifyAuthorizedFactory','getManufacturerFavorites','getManufacturerAnnualTopList','getShopInfo', 'getShopList','getNearbyShops'];

    /**
     * 店铺信息
     */
    public function getShopInfo()
    {
        if($this->request->isGet()) {
            $shopId = $this->request->get('shop_id', '', 'trim');
            $validate = Validate::rule('shop_id', 'require|integer|gt:0');
            if(!$validate->check(['shop_id'=>$shopId])) {
                return JsonServer::error($validate->getError());
            }
            $data = ShopLogic::getShopInfo($shopId, $this->user_id, input());
            return JsonServer::success('获取店铺信息成功', $data);
        }else{
            return JsonServer::error('请求类型错误');
        }
    }

    /**
     * 店铺信息
     */
    public function qualifications()
    {
        if($this->request->isGet()) {
            $shopId = $this->request->get('shop_id', '', 'trim');
            $validate = Validate::rule('shop_id', 'require|integer|gt:0');
            if(!$validate->check(['shop_id'=>$shopId])) {
                return JsonServer::error($validate->getError());
            }

            $detail = ArticleLogic::Shopdetail($shopId);
            return JsonServer::success('获取成功', $detail);
        }else{
            return JsonServer::error('请求类型错误');
        }
    }

    /**
     * 店铺列表
     */
    public function getShopList()
    {
        if($this->request->isGet()) {
            $get = $this->request->get();
            $get['page_no'] =$get['page_no']??$this->page_no;
            $get['page_size'] = $this->page_size;
            // 传递用户ID用于兴趣排序
            $get['user_id'] = $this->user_id;
            $data = ShopLogic::getShopList($get);
            return JsonServer::success('获取店铺列表成功', $data);
        }else{
            return JsonServer::error('请求类型错误');
        }
    }


    /**
     * @notes 附近店铺列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/9/20 4:29 下午
     */
    public function getNearbyShops()
    {
        if($this->request->isGet()) {
            $get = $this->request->get();
            $get['page_no'] = $this->page_no;
            $get['page_size'] = $this->page_size;
            $data = ShopLogic::getNearbyShops($get);
            return JsonServer::success('获取店铺列表成功', $data);
        }else{
            return JsonServer::error('请求类型错误');
        }
    }


    /**
     * 店铺列表
     * 找厂家-年度榜
     */
    public function getManufacturerAnnualTopList()
    {
        if($this->request->isGet()) {
            $get = $this->request->get();
            $get['page_no'] = $this->page_no;
            $get['page_size'] = $this->page_size;
            $data = ShopLogic::getManufacturerAnnualTopList($get);
            return JsonServer::success('获取店铺列表成功', $data);
        }else{
            return JsonServer::error('请求类型错误');
        }
    }


    /**
     * 店铺列表
     * 找厂家-收藏榜
     */
    public function getManufacturerFavorites()
    {
        if($this->request->isGet()) {
            $get = $this->request->get();
            $get['page_no'] = $this->page_no;
            $get['page_size'] = $this->page_size;
            $data = ShopLogic::getManufacturerFavorites($get);
            return JsonServer::success('获取店铺列表成功', $data);
        }else{
            return JsonServer::error('请求类型错误');
        }
    }



    /**
     * 店铺列表
     * 找厂家-权威验厂
     */
    public function getverifyAuthorizedFactory()
    {
        if($this->request->isGet()) {
            $get = $this->request->get();
            $get['page_no'] = $this->page_no;
            $get['page_size'] = $this->page_size;
            $data = ShopLogic::getverifyAuthorizedFactory($get);
            return JsonServer::success('获取店铺列表成功', $data);
        }else{
            return JsonServer::error('请求类型错误');
        }
    }

    /**
     * 店铺列表
     * 找厂家-集采联盟
     */
    public function getexecuteCollectiveProcurement()
    {
        if($this->request->isGet()) {
            $get = $this->request->get();
            $get['page_no'] = $this->page_no;
            $get['page_size'] = $this->page_size;
            $data = ShopLogic::getexecuteCollectiveProcurement($get);
            return JsonServer::success('获取店铺列表成功', $data);
        }else{
            return JsonServer::error('请求类型错误');
        }
    }

    /**
     * 店铺列表
     * 找厂家-热销企业
     */
    public function getHotManufacturers()
    {
        if($this->request->isGet()) {
            $get = $this->request->get();
            $get['page_no'] = $this->page_no;
            $get['page_size'] = $this->page_size;
            $data = ShopLogic::getHotManufacturers($get);
            return JsonServer::success('获取店铺列表成功', $data);
        }else{
            return JsonServer::error('请求类型错误');
        }
    }
}