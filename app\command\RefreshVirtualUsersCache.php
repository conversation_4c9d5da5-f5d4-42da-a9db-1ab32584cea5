<?php
namespace app\command;

use app\api\logic\GoodsDynamicLogic;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 清除商品动态信息缓存定时任务
 * 每天2点执行，清除所有商品的动态信息缓存
 */
class RefreshVirtualUsersCache extends Command
{
    protected function configure()
    {
        $this->setName('refresh:virtual-users-cache')
            ->setDescription('清除商品动态信息缓存');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始清除商品动态信息缓存...');

        try {
            $result = GoodsDynamicLogic::clearGoodsDynamicCache();

            if ($result) {
                $output->writeln('商品动态信息缓存清除成功！');
                return 0;
            } else {
                $output->writeln('商品动态信息缓存清除失败！');
                return 1;
            }
        } catch (\Exception $e) {
            $output->writeln('商品动态信息缓存清除异常：' . $e->getMessage());
            return 1;
        }
    }
}
